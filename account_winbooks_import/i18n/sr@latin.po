# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_winbooks_import
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-08 18:49+0000\n"
"PO-Revision-Date: 2025-03-26 20:46+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid ""
"<span invisible=\"only_open\"/>\n"
"                    <span class=\"text-warning mb4 mt16\" "
"invisible=\"only_open\">\n"
"                        The export of data from Winbooks for closed years "
"might contain unbalanced entries. However if you want to try to import "
"everything, Odoo will set the difference of balance in a Suspense Account.\n"
"                    </span>"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_winbooks_import_wizard
msgid "Account Winbooks import wizard"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_import_summary
msgid "Account import summary view"
msgstr ""

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Accounting Settings"
msgstr ""

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"At least one automatic counterpart has been created at import. This is "
"probably an error. Please check entry lines with reference: Counterpart "
"(generated at import from Winbooks)"
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Cancel"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_res_company
msgid "Companies"
msgstr ""

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Company Settings"
msgstr ""

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Counterpart (generated at import from Winbooks)"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__create_date
msgid "Created on"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__zip_file
msgid "File"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__id
msgid "ID"
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Import"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_analytic_ids
msgid "Import Summary Analytic"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_analytic_line_ids
msgid "Import Summary Analytic Line"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_len_analytic
msgid "Import Summary Len Analytic"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_import_summary__import_summary_len_analytic_line
msgid "Import Summary Len Analytic Line"
msgstr ""

#. module: account_winbooks_import
#. odoo-javascript
#: code:addons/account_winbooks_import/static/src/xml/account_winbooks_import.xml:0
msgid "Import WBK"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid "Import only open years"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_move_line__winbooks_line_id
msgid "Line ID that was used in Winbooks"
msgstr ""

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid ""
"No data zip in the main archive. Please use the complete Winbooks export."
msgstr ""

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "Please define the country on your company."
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.winbooks_import_form
msgid "Stage Search"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid "Suspense Account Code"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model,name:account_winbooks_import.model_account_tax_group
msgid "Tax Group"
msgstr ""

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "The code for the Suspense Account you entered doesn't match any account"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__suspense_code
msgid ""
"This is the code of the account in which you want to put the counterpart of "
"unbalanced moves. This might be an account from your Winbooks data, or an "
"account that you created in Odoo before the import."
msgstr ""

#. module: account_winbooks_import
#: model:ir.actions.act_window,name:account_winbooks_import.winbooks_import_action
msgid "Winbooks Import"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,field_description:account_winbooks_import.field_account_move_line__winbooks_line_id
msgid "Winbooks Line"
msgstr ""

#. module: account_winbooks_import
#: model:ir.model.fields,help:account_winbooks_import.field_account_winbooks_import_wizard__only_open
msgid ""
"Years closed in Winbooks are likely to have incomplete data. The counter "
"part of incomplete entries will be set in a suspense account"
msgstr ""

#. module: account_winbooks_import
#. odoo-python
#: code:addons/account_winbooks_import/wizard/import_wizard.py:0
msgid "You should install a Fiscal Localization first."
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.account_import_summary_form
msgid "account analytic lines imported"
msgstr ""

#. module: account_winbooks_import
#: model_terms:ir.ui.view,arch_db:account_winbooks_import.account_import_summary_form
msgid "account analytics imported"
msgstr ""
