# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_dhl_rest
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-30 18:44+0000\n"
"PO-Revision-Date: 2025-05-30 18:44+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__0
msgid "0 - Logistics Services"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__1
msgid "1 - Domestic Express 12:00"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__2
msgid "2 - B2C"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__3
msgid "3 - B2C"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__4
msgid "4 - Jetline"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__5
msgid "5 - Sprintline"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__6
msgid "6 - Secureline"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__6x4_a4_pdf
msgid "6X4_A4_PDF"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__6x4_pdf
msgid "6X4_PDF"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__6x4_thermal
msgid "6X4_thermal"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__7
msgid "7 - Express Easy"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__8
msgid "8 - Express Easy"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__8x4_a4_pdf
msgid "8X4_A4_PDF"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__8x4_a4_tc_pdf
msgid "8X4_A4_TC_PDF"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__8x4_ci_pdf
msgid "8X4_CI_PDF"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__8x4_ci_thermal
msgid "8X4_CI_thermal"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__8x4_pdf
msgid "8X4_PDF"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__8x4_ru_a4_pdf
msgid "8X4_RU_A4_PDF"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_template__8x4_thermal
msgid "8X4_thermal"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__9
msgid "9 - Europack"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__a
msgid "A - Auto Reversals"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_region_code__am
msgid "America"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_region_code__ap
msgid "Asia Pacific"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__b
msgid "B - Break Bulk Express"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__c
msgid "C - Medical Express"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,help:delivery_dhl_rest.field_delivery_carrier__dhl_dutiable
msgid "Check this if your package is dutiable."
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/dhl_request.py:0
msgid "Could not decode the response from DHL."
msgstr ""

#. module: delivery_dhl_rest
#: model_terms:ir.ui.view,arch_db:delivery_dhl_rest.view_delivery_carrier_form_with_provider_dhl
msgid "Custom Data"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__d
msgid "D - Express Worldwide"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__delivery_type__dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__stock_package_type__package_carrier_type__dhl_rest
msgid "DHL"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_api_key
msgid "DHL API Key"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_api_secret
msgid "DHL API Secret"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/dhl_request.py:0
msgid "DHL API key is missing, please modify your delivery method settings."
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/dhl_request.py:0
msgid ""
"DHL API secret is missing, please modify your delivery method settings."
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_account_number
msgid "DHL Account Number"
msgstr ""

#. module: delivery_dhl_rest
#: model:delivery.carrier,name:delivery_dhl_rest.delivery_carrier_dhl_be_dom
#: model:product.template,name:delivery_dhl_rest.product_product_delivery_dhl_be_dom_product_template
msgid "DHL BE"
msgstr ""

#. module: delivery_dhl_rest
#: model_terms:ir.ui.view,arch_db:delivery_dhl_rest.view_delivery_carrier_form_with_provider_dhl
msgid "DHL Configuration"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/delivery_dhl.py:0
msgid "DHL Documents"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_product_code
msgid "DHL Product"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/dhl_request.py:0
msgid ""
"DHL account number is missing, please modify your delivery method settings."
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_default_package_type_id
msgid "DHLxw Package Type"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_duty_payment
msgid "Dhl Duty Payment"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_dutiable
msgid "Dutiable Material"
msgstr ""

#. module: delivery_dhl_rest
#: model_terms:ir.ui.view,arch_db:delivery_dhl_rest.view_delivery_carrier_form_with_provider_dhl
msgid "Duties paid by"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__e
msgid "E - Express 9:00"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_image_format__epl2
msgid "EPL2"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_region_code__eu
msgid "Europe"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_extra_data_rate_request
msgid "Extra data for rate requests"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_extra_data_return_request
msgid "Extra data for return requests"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_extra_data_ship_request
msgid "Extra data for ship requests"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,help:delivery_dhl_rest.field_delivery_carrier__dhl_extra_data_rate_request
#: model:ir.model.fields,help:delivery_dhl_rest.field_delivery_carrier__dhl_extra_data_return_request
#: model:ir.model.fields,help:delivery_dhl_rest.field_delivery_carrier__dhl_extra_data_ship_request
msgid ""
"Extra data to be sent in the request. It should be JSON-formatted.\n"
"This functionality is advanced/technical and should only be used if you know what you are doing.\n"
"\n"
"Example of a valid value: ```\n"
"\"content\": {\"packages\": {\"description\": \"amazing package\"}}\n"
"```\n"
"\n"
"With the above example, the description of each package will be updated.\n"
"For more information, please refer to the DHL API documentation: https://developer.dhl.com/api-reference/dhl-express-mydhl-api.\n"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__f
msgid "F - Freight Worldwide"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__g
msgid "G - Domestic Economy Select"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__h
msgid "H - Economy Select"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__i
msgid "I - Break Bulk Economy"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_unit_system__imperial
msgid "Imperial"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/delivery_dhl.py:0
msgid "Invalid syntax for DHL extra data."
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__j
msgid "J - Jumbo Box"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__k
msgid "K - Express 9:00"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__l
msgid "L - Express 10:30"
msgstr ""

#. module: delivery_dhl_rest
#: model_terms:ir.ui.view,arch_db:delivery_dhl_rest.view_delivery_carrier_form_with_provider_dhl
msgid "Label Format"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_label_image_format
msgid "Label Image Format"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_label_template
msgid "Label Template"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__m
msgid "M - Express 10:30"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_unit_system__metric
msgid "Metric"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__n
msgid "N - Domestic Express"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__o
msgid "O - DOM Express 10:30"
msgstr ""

#. module: delivery_dhl_rest
#: model_terms:ir.ui.view,arch_db:delivery_dhl_rest.view_delivery_carrier_form_with_provider_dhl
msgid "Options"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__p
msgid "P - Express Worldwide"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_image_format__pdf
msgid "PDF"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/dhl_request.py:0
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__q
msgid "Q - Medical Express"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__r
msgid "R - GlobalMail Business"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_duty_payment__r
msgid "Recipient"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_region_code
msgid "Region"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__s
msgid "S - Same Day"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_duty_payment__s
msgid "Sender"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/delivery_dhl.py:0
msgid "Shipment created into DHL"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model,name:delivery_dhl_rest.model_delivery_carrier
msgid "Shipping Methods"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/dhl_request.py:0
msgid "Something went wrong, please try again later!!"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model,name:delivery_dhl_rest.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__t
msgid "T - Express 12:00"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/dhl_request.py:0
msgid ""
"The address of the customer is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/dhl_request.py:0
msgid ""
"The address of your company warehouse is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/dhl_request.py:0
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/delivery_dhl.py:0
msgid ""
"The package cannot be created because the total weight of the products in "
"the picking is 0.0 %s"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/delivery_dhl.py:0
msgid "The planned date for the shipment must be in the future."
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/delivery_dhl.py:0
msgid ""
"There is no price available for this shipping, you should rather try with "
"the DHL product %s"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/delivery_dhl.py:0
msgid "Tracking Number"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__u
msgid "U - Express Worldwide"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields,field_description:delivery_dhl_rest.field_delivery_carrier__dhl_unit_system
msgid "Unit System"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__v
msgid "V - Europack"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__w
msgid "W - Economy Select"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__x
msgid "X - Express Envelope"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__y
msgid "Y - Express 12:00"
msgstr ""

#. module: delivery_dhl_rest
#. odoo-python
#: code:addons/delivery_dhl_rest/models/delivery_dhl.py:0
msgid "You can't cancel DHL shipping without pickup date."
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_product_code__z
msgid "Z - Destination Charges"
msgstr ""

#. module: delivery_dhl_rest
#: model:ir.model.fields.selection,name:delivery_dhl_rest.selection__delivery_carrier__dhl_label_image_format__zpl2
msgid "ZPL2"
msgstr ""
