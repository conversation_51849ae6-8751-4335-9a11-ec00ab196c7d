# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_usps_rest
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 15:41+0000\n"
"PO-Revision-Date: 2025-02-10 15:41+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__3d
msgid "3D - 3-Digit"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__3n
msgid "3N - 3-Digit Dimensional Rectangular"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__3r
msgid "3R - 3-Digit Dimensional Nonrectangular"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_label_size__3x5fmp
msgid "3x5"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_label_size__4x5label
msgid "4x5"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_label_size__4x6label
msgid "4x6"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__5d
msgid "5D - 5-Digit"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__ba
msgid "BA - Basic"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__bb
msgid "BB - Mixed NDC"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__bound_printed_matter
msgid "Bound Printed Matter"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__cm
msgid "CM - USPS Connect® Local Mail"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__cp
msgid "CP - Cubic Parcel"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_crid
msgid "CRID"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__commercial_sample
msgid "Commercial Sample"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid "Company phone number is invalid. Please insert a US phone number."
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_rest_content_type
msgid "Content Type (REST)"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid "Couldn't decode the response from USPS."
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid ""
"Couldn't decode the response from USPS.\n"
"%s"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__cremated_remains
msgid "Cremated Remains"
msgstr ""

#. module: delivery_usps_rest
#: model_terms:ir.ui.view,arch_db:delivery_usps_rest.view_delivery_carrier_form_with_provider_usps
msgid "Custom Data"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__dc
msgid "DC - NDC"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__de
msgid "DE - SCF"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__df
msgid "DF - 5-Digit"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__dn
msgid "DN - Dimensional Nonrectangular"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__dr
msgid "DR - Dimensional Rectangular"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__dangerous_goods
msgid "Dangerous Goods"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_delivery_nature
msgid "Delivery Nature"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__document
msgid "Document"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_delivery_nature__domestic
msgid "Domestic"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_domestic_rating_indicator
msgid "Domestic Rating Indicator"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__e4
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__e4
msgid ""
"E4 - Priority Mail Express Flat Rate Envelope - Post Office To Addressee"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__e6
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__e6
msgid "E6 - Priority Mail Express Legal Flat Rate Envelope"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__e7
msgid "E7 - Priority Mail Express Legal Flat Rate Envelope Sunday / Holiday"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_eps_account_number
msgid "EPS Account Number"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_extra_data_payment_token_request
msgid "Extra Data for Payment Token Requests"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_extra_data_rate_request
msgid "Extra Data for Rate Requests"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_extra_data_shipment_request
msgid "Extra Data for Shipment Requests"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,help:delivery_usps_rest.field_delivery_carrier__usps_extra_data_payment_token_request
#: model:ir.model.fields,help:delivery_usps_rest.field_delivery_carrier__usps_extra_data_rate_request
#: model:ir.model.fields,help:delivery_usps_rest.field_delivery_carrier__usps_extra_data_shipment_request
msgid ""
"Extra data to be sent in the request. It should be a JSON-formatted string. For example:\n"
"This functionality is advanced/technical and should only be used if you know what you are doing.\n"
"Example of a valid value: ```\n"
"\"packageDescription\": {\"extraServices\": [920] }\n"
"```\n"
"With the above example, the service (920) will be added to the shipment request.\n"
"For more information, please refer to the USS API documentation: https://developer.usps.com/apis.\n"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__fa
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__fa
msgid "FA - Legal Flat Rate Envelope"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__fb
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__fb
msgid "FB - Medium Flat Rate Box/Large Flat Rate Bag"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__fe
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__fe
msgid "FE - Flat Rate Envelope"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__fp
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__fp
msgid "FP - Padded Flat Rate Envelope"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__fs
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__fs
msgid "FS - Small Flat Rate Box"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_service__first-class_package_international_service
msgid "First-Class Package International Service"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__first-class_package_service
msgid "First-Class Package Service"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_processing_category__flats
msgid "Flats"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__gift
msgid "Gift"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_service__global_express_guaranteed
msgid "Global Express Guaranteed"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__humanitarian_donations
msgid "Humanitarian Donations"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_delivery_nature__international
msgid "International"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_international_rating_indicator
msgid "International Rating Indicator"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/delivery_usps.py:0
msgid "Invalid syntax for USPS extra data."
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_processing_category__irregular
msgid "Irregular"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_label_file_type__jpg
msgid "JPG"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__lc
msgid "LC - USPS Connect® Local Single Piece"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__lf
msgid "LF - Flat Rate Box"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__ll
msgid "LL - Large Flat Rate Bag"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__lo
msgid "LO - USPS Connect® Local Oversized"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__ls
msgid "LS - Small Flat Rate Bag"
msgstr ""

#. module: delivery_usps_rest
#: model_terms:ir.ui.view,arch_db:delivery_usps_rest.view_delivery_carrier_form_with_provider_usps
msgid "Label Format"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_processing_category__letters
msgid "Letters"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__library_mail
msgid "Library Mail"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_mid
msgid "MID"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_processing_category__machinable
msgid "Machinable"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_manifest_mid
msgid "Manifest MID"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__media_mail
msgid "Media Mail"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__medical_supplies
msgid "Medical Supplies"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__merchandise
msgid "Merchandise"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__np
msgid "NP - Non-Presorted"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_processing_category__non_machinable
msgid "Non-Machinable"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__non_negotiable_document
msgid "Non-Negotiable Document"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__os
msgid "OS - Oversized"
msgstr ""

#. module: delivery_usps_rest
#: model_terms:ir.ui.view,arch_db:delivery_usps_rest.view_delivery_carrier_form_with_provider_usps
msgid "Options"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__other
msgid "Other"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__p5
msgid "P5 - Cubic Soft Pack Tier 1"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__p6
msgid "P6 - Cubic Soft Pack Tier 2"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__p7
msgid "P7 - Cubic Soft Pack Tier 3"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__p8
msgid "P8 - Cubic Soft Pack Tier 4"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__p9
msgid "P9 - Cubic Soft Pack Tier 5"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__pa
msgid "PA - Priority Mail Express International Single Piece"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__pa
msgid "PA - Priority Mail Express Single Piece"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_label_file_type__pdf
msgid "PDF"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__pl
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__pl
msgid "PL - Large Flat Rate Box"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__pm
msgid "PM - Large Flat Rate Box APO/FPO/DPO"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__pr
msgid "PR - Presorted"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__parcel_select
msgid "Parcel Select"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__parcel_select_lightweight
msgid "Parcel Select Lightweight"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__pharmaceuticals
msgid "Pharmaceuticals"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,help:delivery_usps_rest.field_delivery_carrier__usps_processing_category
msgid "Please check on USPS website to check your package processing category"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid "Please enter a valid ZIP code in recipient address"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid "Please enter a valid ZIP code in your Company address"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid ""
"Please set country USA in your company address, Service is only available "
"for USA"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__priority_mail
msgid "Priority Mail"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__priority_mail_express
msgid "Priority Mail Express"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_service__priority_mail_express_international
msgid "Priority Mail Express International"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_service__priority_mail_international
msgid "Priority Mail International"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_processing_category
msgid "Processing Category"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__q0
msgid "Q0 - Cubic Soft Pack Tier 10"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__q6
msgid "Q6 - Cubic Soft Pack Tier 6"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__q7
msgid "Q7 - Cubic Soft Pack Tier 7"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__q8
msgid "Q8 - Cubic Soft Pack Tier 8"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__q9
msgid "Q9 - Cubic Soft Pack Tier 9"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/delivery_usps.py:0
msgid "Return Shipment created into USPS"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__returned_goods
msgid "Returned Goods"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__sn
msgid "SN - SCF Dimensional Nonrectangular"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__sp
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_international_rating_indicator__sp
msgid "SP - Single Piece"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_rating_indicator__sr
msgid "SR - SCF Dimensional Rectangular"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_label_file_type__svg
msgid "SVG"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_content_type__sample
msgid "Sample"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/delivery_usps.py:0
msgid "Shipment created into USPS"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/delivery_usps.py:0
msgid "Shipment with tracking number %(tracking_ref)s has been cancelled"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model,name:delivery_usps_rest.model_delivery_carrier
msgid "Shipping Methods"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model,name:delivery_usps_rest.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_rest_label_file_type__tiff
msgid "TIFF"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid ""
"The address of your company is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/delivery_usps.py:0
msgid ""
"The package cannot be created because the total weight of the products in "
"the picking is 0.0 %s"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid ""
"The recipient address is missing or wrong (Missing field(s) :  \n"
" %s)"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/delivery_usps.py:0
msgid ""
"There is no price available for this shipping, please choose another service"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/delivery_usps.py:0
msgid "Tracking Number:"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__delivery_type__usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__stock_package_type__package_carrier_type__usps_rest
msgid "USPS"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_api_key
msgid "USPS API Key"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_api_secret
msgid "USPS API Secret"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_access_token
msgid "USPS Access Token"
msgstr ""

#. module: delivery_usps_rest
#: model_terms:ir.ui.view,arch_db:delivery_usps_rest.view_delivery_carrier_form_with_provider_usps
msgid "USPS Configuration"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__usps_connect_local
msgid "USPS Connect Local"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__usps_connect_mail
msgid "USPS Connect Mail"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__usps_connect_next_day
msgid "USPS Connect Next Day"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__usps_connect_regional
msgid "USPS Connect Regional"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__usps_connect_same_day
msgid "USPS Connect Same Day"
msgstr ""

#. module: delivery_usps_rest
#: model:delivery.carrier,name:delivery_usps_rest.delivery_carrier_usps_domestic
#: model:product.template,name:delivery_usps_rest.product_product_delivery_usps_domestic_product_template
msgid "USPS Domestic"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_domestic_service
msgid "USPS Domestic Service"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid ""
"USPS Domestic is used only to ship inside of the USA. Please change the "
"delivery method into USPS International."
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__usps_ground_advantage
msgid "USPS Ground Advantage"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__usps_ground_advantage_return_service
msgid "USPS Ground Advantage Return Service"
msgstr ""

#. module: delivery_usps_rest
#: model:delivery.carrier,name:delivery_usps_rest.delivery_carrier_usps_international
#: model:product.template,name:delivery_usps_rest.product_product_delivery_usps_international_product_template
msgid "USPS International"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_international_service
msgid "USPS International Service"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid ""
"USPS International is used only to ship outside of the USA. Please change "
"the delivery method into USPS Domestic."
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_rest_label_file_type
msgid "USPS Label File Type"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_label_size
msgid "USPS Label Size"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_default_package_type_id
msgid "USPS Package Type"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields,field_description:delivery_usps_rest.field_delivery_carrier__usps_payment_token
msgid "USPS Payment Token"
msgstr ""

#. module: delivery_usps_rest
#: model:ir.model.fields.selection,name:delivery_usps_rest.selection__delivery_carrier__usps_domestic_service__usps_retail_ground
msgid "USPS Retail Ground"
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid "You need to set your USPS API Key and Secret to use this feature."
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid ""
"You need to set your USPS CRID, MID, Manifest MID and EPS Account Number to "
"use this feature."
msgstr ""

#. module: delivery_usps_rest
#. odoo-python
#: code:addons/delivery_usps_rest/models/usps_request.py:0
msgid "Your USPS API Key and Secret are invalid."
msgstr ""
