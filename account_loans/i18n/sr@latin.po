# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_loans
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:45+0000\n"
"PO-Revision-Date: 2025-03-26 20:45+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "1st amortization schedule"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__compounding_method__30a/360
msgid "30A/360"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__compounding_method__30e/360
msgid "30E/360"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__compounding_method__30e/360_isda
msgid "30E/360 ISDA"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__compounding_method__30u/360
msgid "30U/360"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid ""
"<span class=\"oe_inline\" invisible=\"duration == 1\">months</span>\n"
"                                        <span class=\"oe_inline\" invisible=\"duration != 1\">month</span>"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_compute_wizard
msgid ""
"<span class=\"oe_inline\" invisible=\"loan_term == 1\">years</span>\n"
"                                    <span class=\"oe_inline\" invisible=\"loan_term != 1\">year</span>"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_compute_wizard
msgid "<span class=\"oe_inline\">%</span>"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__compounding_method__a/360
msgid "A/360"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__compounding_method__a/365f
msgid "A/365F"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__compounding_method__a/a_afb
msgid "A/A AFB"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__compounding_method__a/a_isda
msgid "A/A ISDA"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__active
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__active
msgid "Active"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_close_wizard
msgid "All draft entries after the"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Amortization schedule"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__amount_borrowed
msgid "Amount Borrowed"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__amount_borrowed_difference
msgid "Amount Borrowed Difference"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_close_wizard
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_compute_wizard
msgid "Apply"
msgstr ""

#. module: account_loans
#: model:ir.model,name:account_loans.model_account_asset_group
#: model:ir.model.fields,field_description:account_loans.field_account_loan__asset_group_id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__loan_asset_group_id
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_search_view
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Asset Group"
msgstr ""

#. module: account_loans
#: model:ir.model,name:account_loans.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__payment_end_of_month__at_anniversary
msgid "At Anniversary"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/wizard/account_loan_compute_wizard.py:0
msgid "Balance"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Cancel"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan__state__cancelled
msgid "Cancelled"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Close"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_close_wizard__date
msgid "Close Date"
msgstr ""

#. module: account_loans
#: model:ir.actions.act_window,name:account_loans.action_view_account_loan_close_wizard
#: model:ir.model,name:account_loans.model_account_loan_close_wizard
msgid "Close Loan Wizard"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan__state__closed
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_search_view
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Closed"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_search_view
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Closed Loans"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/wizard/account_loan_close_wizard.py:0
msgid "Closed on the %(date)s"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__company_id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__company_id
msgid "Company"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__compounding_method
msgid "Compounding Method"
msgstr ""

#. module: account_loans
#. odoo-javascript
#: code:addons/account_loans/static/src/components/loans/file_upload.xml:0
msgid "Compute"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "Compute New Loan"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Confirm"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__count_linked_assets
msgid "Count Linked Assets"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_asset__count_linked_loans
#: model:ir.model.fields,field_description:account_loans.field_account_asset_group__count_linked_loans
msgid "Count Linked Loans"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__create_uid
#: model:ir.model.fields,field_description:account_loans.field_account_loan_close_wizard__create_uid
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__create_uid
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__create_uid
msgid "Created by"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__create_date
#: model:ir.model.fields,field_description:account_loans.field_account_loan_close_wizard__create_date
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__create_date
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__create_date
msgid "Created on"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__currency_id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__currency_id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__currency_id
msgid "Currency"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_search_view
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Current"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/wizard/account_loan_compute_wizard.py:0
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__date
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Date"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_close_wizard
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_compute_wizard
msgid "Discard"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_close_wizard__display_name
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__display_name
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__display_name
msgid "Display Name"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan__state__draft
msgid "Draft"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_search_view
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Draft & Running Loans"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "Due"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__duration
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Duration"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__duration_difference
msgid "Duration Difference"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__end_date
msgid "End Date"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan_compute_wizard__payment_end_of_month__end_of_month
msgid "End of Month"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,help:account_loans.field_account_loan_line__generated_move_ids
msgid "Entries that we generated from this loan line"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__expense_account_id
msgid "Expense Account"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__first_payment_date
msgid "First Payment"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__generated_move_ids
msgid "Generated Entries"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_bank_statement_line__generating_loan_line_id
#: model:ir.model.fields,field_description:account_loans.field_account_move__generating_loan_line_id
msgid "Generating Loan Line"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_search_view
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Group By"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__has_message
msgid "Has Message"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_close_wizard__id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__id
msgid "ID"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,help:account_loans.field_account_loan__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_loans
#: model:ir.model.fields,help:account_loans.field_account_loan__message_has_error
#: model:ir.model.fields,help:account_loans.field_account_loan__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
#: code:addons/account_loans/wizard/account_loan_compute_wizard.py:0
#: model:ir.model.fields,field_description:account_loans.field_account_loan__interest
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__interest
msgid "Interest"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__interest_difference
msgid "Interest Difference"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__interest_rate
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_compute_wizard
msgid "Interest Rate"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/wizard/account_loan_compute_wizard.py:0
msgid "Interest Rate must be between 0 and 100"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_list_view
msgid "Interests"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_bank_statement_line__is_loan_payment_move
#: model:ir.model.fields,field_description:account_loans.field_account_move__is_loan_payment_move
msgid "Is Loan Payment Move"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__is_payment_move_posted
msgid "Is Payment Move Posted"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__is_wrong_date
msgid "Is Wrong Date"
msgstr ""

#. module: account_loans
#: model:ir.model,name:account_loans.model_account_journal
#: model:ir.model.fields,field_description:account_loans.field_account_loan__journal_id
msgid "Journal"
msgstr ""

#. module: account_loans
#: model:ir.model,name:account_loans.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: account_loans
#: model:account.journal,name:account_loans.account_loans_journal_loan
msgid "Journal Loan Demo"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__write_uid
#: model:ir.model.fields,field_description:account_loans.field_account_loan_close_wizard__write_uid
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__write_uid
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__write_date
#: model:ir.model.fields,field_description:account_loans.field_account_loan_close_wizard__write_date
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__write_date
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,help:account_loans.field_account_bank_statement_line__generating_loan_line_id
#: model:ir.model.fields,help:account_loans.field_account_move__generating_loan_line_id
msgid "Line of the loan that generated this entry"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
#: model:ir.model.fields,field_description:account_loans.field_account_loan__linked_assets_ids
msgid "Linked Assets"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_asset.py:0
msgid "Linked loans"
msgstr ""

#. module: account_loans
#: model:ir.model,name:account_loans.model_account_loan
#: model:ir.model.fields,field_description:account_loans.field_account_bank_statement_line__loan_id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_close_wizard__loan_id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__loan_id
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__loan_id
#: model:ir.model.fields,field_description:account_loans.field_account_move__loan_id
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_search_view
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Loan"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__loan_amount
msgid "Loan Amount"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/wizard/account_loan_compute_wizard.py:0
msgid "Loan Amount must be positive"
msgstr ""

#. module: account_loans
#: model:ir.actions.act_window,name:account_loans.action_view_account_loan_compute_wizard
#: model:ir.model,name:account_loans.model_account_loan_compute_wizard
msgid "Loan Compute Wizard"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__date
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__loan_date
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_search_view
msgid "Loan Date"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "Loan Entries"
msgstr ""

#. module: account_loans
#: model:ir.model,name:account_loans.model_account_loan_line
msgid "Loan Line"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__line_ids
msgid "Loan Lines"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Loan Settings"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__loan_term
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_compute_wizard
msgid "Loan Term"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/wizard/account_loan_compute_wizard.py:0
msgid "Loan Term must be positive"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_list_view
msgid "Loan lines"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__display_name
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Loan name"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.asset_group_form_view_inherit_loan
msgid "Loan(s)"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
#: model:ir.actions.act_window,name:account_loans.action_view_account_loans
#: model:ir.ui.menu,name:account_loans.menu_action_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_list_view
msgid "Loans"
msgstr ""

#. module: account_loans
#: model:ir.actions.act_window,name:account_loans.action_view_account_loans_analysis
#: model:ir.ui.menu,name:account_loans.menu_action_loans_analysis
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_pivot_view
msgid "Loans Analysis"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__long_term_account_id
msgid "Long Term Account"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__long_term_theoretical_balance
msgid "Long-Term"
msgstr ""

#. module: account_loans
#: model_terms:ir.actions.act_window,help:account_loans.action_view_account_loans
msgid "Manage Your Acquired Loans with Automated Adjustments."
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_ids
msgid "Messages"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_journal__loan_properties_definition
msgid "Model Properties"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__name
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__loan_name
msgid "Name"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__nb_posted_entries
msgid "Nb Posted Entries"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,help:account_loans.field_account_loan__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,help:account_loans.field_account_loan__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_move.py:0
msgid "Original Loan"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__outstanding_balance
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__outstanding_balance
msgid "Outstanding Balance"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/wizard/account_loan_compute_wizard.py:0
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__payment_end_of_month
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__payment
msgid "Payment"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_list_view
msgid "Payments"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "Please add a name before computing the loan"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Posted Entries"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__preview
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_compute_wizard
msgid "Preview"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
#: code:addons/account_loans/wizard/account_loan_compute_wizard.py:0
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__principal
msgid "Principal"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "Principal & Interest"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_list_view
msgid "Principals"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__loan_properties
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Properties"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__rating_ids
msgid "Ratings"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "Reclassification LT - ST"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Related Asset(s)"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.view_move_form_inherit_loan
msgid "Related Loan"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_asset_form_inherit_loan
msgid "Related Loan(s)"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_asset__linked_loans_ids
#: model:ir.model.fields,field_description:account_loans.field_account_asset_group__linked_loan_ids
msgid "Related Loans"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Reset"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "Reversal reclassification LT - ST"
msgstr ""

#. module: account_loans
#: model:ir.model.fields.selection,name:account_loans.selection__account_loan__state__running
msgid "Running"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_search_view
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_search_view
msgid "Search Loan"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_form_view
msgid "Set to Draft"
msgstr ""

#. module: account_loans
#: model_terms:ir.actions.act_window,help:account_loans.action_view_account_loans
msgid ""
"Set up your amortization schedule, or import it, and let Odoo handle the "
"monthly interest and principal adjustments automatically."
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__short_term_account_id
msgid "Short Term Account"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__short_term_theoretical_balance
msgid "Short-Term"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__skip_until_date
msgid "Skip until"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__start_date
#: model:ir.model.fields,field_description:account_loans.field_account_loan_compute_wizard__start_date
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_list_view
msgid "Start Date"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__state
#: model:ir.model.fields,field_description:account_loans.field_account_loan_line__loan_state
msgid "Status"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/wizard/account_loan_compute_wizard.py:0
msgid "The First Payment Date must be before the end of the loan."
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "The amount borrowed must be positive"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "The duration must be positive"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "The interest must be positive"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "The loan accounts should be set."
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid ""
"The loan amount %(loan_amount)s should be equal to the sum of the "
"principals: %(principal_sum)s (difference %(principal_difference)s)"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "The loan date should be earlier than the loan lines date."
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "The loan duration should be equal to the number of loan lines."
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid ""
"The loan interest should be equal to the sum of the loan lines interest."
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "The loan journal should be set."
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "The loan name should be set."
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "This entry has been %s"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "This entry has been reversed from %s"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_list_view
msgid "Total Amounts Borrowed"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_list_view
msgid "Total Outstanding Balance"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_list_view
msgid "Total interests"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_list_view
msgid "Total payments"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.account_loan_line_list_view
msgid "Total principals"
msgstr ""

#. module: account_loans
#. odoo-javascript
#: code:addons/account_loans/static/src/components/loans/file_upload.xml:0
msgid "Upload"
msgstr ""

#. module: account_loans
#. odoo-python
#: code:addons/account_loans/models/account_loan.py:0
msgid "Uploaded file"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,help:account_loans.field_account_loan__skip_until_date
msgid ""
"Upon confirmation of the loan, Odoo will ignore the loan lines that are up "
"to this date (included) and not create entries for them. This is useful if "
"you have already manually created entries prior to the creation of this "
"loan."
msgstr ""

#. module: account_loans
#: model:ir.model.fields,field_description:account_loans.field_account_loan__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_loans
#: model:ir.model.fields,help:account_loans.field_account_loan__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account_loans
#: model_terms:ir.ui.view,arch_db:account_loans.view_account_loan_close_wizard
msgid "will be deleted and the loan will be marked as closed."
msgstr ""
