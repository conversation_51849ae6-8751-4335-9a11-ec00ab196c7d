# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * crm_helpdesk
#
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-27 18:48+0000\n"
"PO-Revision-Date: 2025-08-29 15:41+0000\n"
"Last-Translator: Oakarmin Iron <<EMAIL>>\n"
"Language-Team: Burmese <https://translate.odoo.com/projects/odoo-18/"
"crm_helpdesk/my/>\n"
"Language: my\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/wizard/helpdesk_ticket_to_lead.py:0
msgid "An archived ticket cannot be converted into a lead."
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__convert_to
msgid "Conversion Action"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model,name:crm_helpdesk.model_helpdesk_ticket_to_lead
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.helpdesk_ticket_to_lead_view_form
msgid "Convert Ticket to Lead"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/models/helpdesk_ticket.py:0
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.helpdesk_ticket_to_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.helpdesk_ticket_view_form_inherited
msgid "Convert to Lead"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/models/helpdesk_ticket.py:0
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.helpdesk_ticket_to_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.helpdesk_ticket_view_form_inherited
msgid "Convert to Opportunity"
msgstr ""

#. module: crm_helpdesk
#: model:ir.actions.act_window,name:crm_helpdesk.crm_lead_convert2ticket_action
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.crm_lead_convert2ticket_view_form
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.crm_lead_view_form
msgid "Convert to Ticket"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields.selection,name:crm_helpdesk.selection__helpdesk_ticket_to_lead__convert_to__convert
msgid "Convert to opportunity"
msgstr ""

#. module: crm_helpdesk
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.crm_lead_convert2ticket_view_form
msgid "Create Ticket"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields.selection,name:crm_helpdesk.selection__helpdesk_ticket_to_lead__action__create
msgid "Create a new customer"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_crm_lead_convert2ticket__create_uid
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__create_uid
msgid "Created by"
msgstr "ဖန်တီးသူ"

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_crm_lead_convert2ticket__create_date
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__create_date
msgid "Created on"
msgstr "ဖန်တီးချိန်"

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_crm_lead_convert2ticket__partner_id
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__partner_id
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.helpdesk_ticket_to_lead_view_form
msgid "Customer"
msgstr ""

#. module: crm_helpdesk
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.crm_lead_convert2ticket_view_form
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.helpdesk_ticket_to_lead_view_form
msgid "Discard"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_crm_lead_convert2ticket__display_name
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__display_name
msgid "Display Name"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields.selection,name:crm_helpdesk.selection__helpdesk_ticket_to_lead__action__nothing
msgid "Do not link to a customer"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__force_assignment
msgid "Force assignment"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model,name:crm_helpdesk.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_crm_lead_convert2ticket__id
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__id
msgid "ID"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,help:crm_helpdesk.field_helpdesk_ticket_to_lead__force_assignment
msgid ""
"If checked, forces salesman to be updated on updated opportunities even if "
"already set."
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_crm_lead_convert2ticket__write_uid
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__write_uid
msgid "Last Updated by"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_crm_lead_convert2ticket__write_date
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__write_date
msgid "Last Updated on"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_crm_lead_convert2ticket__lead_id
msgid "Lead"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/wizard/crm_lead_convert2ticket.py:0
msgid "Lead Converted"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model,name:crm_helpdesk.model_crm_lead_convert2ticket
msgid "Lead convert to Ticket"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/wizard/crm_lead_convert2ticket.py:0
msgid "Lead converted into ticket %s"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/wizard/helpdesk_ticket_to_lead.py:0
msgid "Lead created"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields.selection,name:crm_helpdesk.selection__helpdesk_ticket_to_lead__action__exist
msgid "Link to an existing customer"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/wizard/helpdesk_ticket_to_lead.py:0
msgid "Opportunity created"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__action
msgid "Related Customer"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__team_id
#: model_terms:ir.ui.view,arch_db:crm_helpdesk.helpdesk_ticket_to_lead_view_form
msgid "Sales Team"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__user_id
msgid "Salesperson"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_crm_lead_convert2ticket__team_id
msgid "Team"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/wizard/helpdesk_ticket_to_lead.py:0
msgid "This lead has been created from ticket: %s"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/wizard/helpdesk_ticket_to_lead.py:0
msgid "This opportunity has been created from ticket: %s"
msgstr ""

#. module: crm_helpdesk
#: model:ir.model.fields,field_description:crm_helpdesk.field_helpdesk_ticket_to_lead__ticket_id
msgid "Ticket"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/wizard/helpdesk_ticket_to_lead.py:0
msgid "Ticket Converted"
msgstr ""

#. module: crm_helpdesk
#. odoo-python
#: code:addons/crm_helpdesk/wizard/crm_lead_convert2ticket.py:0
msgid "Ticket Created"
msgstr ""
