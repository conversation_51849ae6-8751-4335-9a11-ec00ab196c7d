# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_fedex_rest
#
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:44+0000\n"
"PO-Revision-Date: 2025-08-28 06:23+0000\n"
"Last-Translator: Oakarmin Iron <<EMAIL>>\n"
"Language-Team: Burmese <https://translate.odoo.com/projects/odoo-18/"
"delivery_fedex_rest/my/>\n"
"Language: my\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid ""
"<b>After finishing creation of the project, you will see your test API key, secret key and account number:</b>\n"
"                                <br/>"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid ""
"<b>Once your account is created, go to the My Projects tab and create a new API project. Make sure to select the options indicated below:</b>\n"
"                                <br/>"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid ""
"<b>To enable the creation of FedEx shipping labels, the last step is the Certification Process, click on 'Go to API certification' and follow the required steps to get your account certified for label creation.</b>\n"
"                                <br/>These certifications usually require that you contact the FedEx support team by email."
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid ""
"<b>To move to production, generate a production key and link it to a FedEx account number (you can add/create one if necessary), you will receive production API and secret keys:</b>\n"
"                                <br/>"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid ""
"<b>When asked to select APIs for the project, make sure to enable at least the following APIs:</b>\n"
"                                <br/>"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "<span class=\"o_form_label\">Package Length Unit</span>"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid ""
"<span invisible=\"fedex_rest_weight_unit != 'KG'\">Centimeters</span>\n"
"                                <span invisible=\"fedex_rest_weight_unit != 'LB'\">Inches</span>"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_developer_key
msgid "API Key"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Account Number"
msgstr "အကောင့် နံပါတ်"

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_residential_address__always
msgid "Always"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/delivery_fedex.py:0
msgid ""
"Automated return label generation is not supported by FedEx for multi-"
"package shipments. Please generate the return labels manually."
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_residential_address__check
msgid "Check using FedEx Address API"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_droppoff_type__contact_fedex_to_schedule
msgid "Contact FedEx for pickup"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/fedex_request.py:0
msgid "Could not decode response"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,help:delivery_fedex_rest.field_delivery_carrier__fedex_rest_residential_address
msgid ""
"Determines whether to mark the recipient address as residential (to "
"correctly calculate any possible surcharges). Please note: when retrieving "
"this information using the FedEx Address API, we assume that the address is "
"residential unless it is marked explicitly as a BUSINESS address."
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_droppoff_type__dropoff_at_fedex_location
msgid "Drop off at FedEx location"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Duties paid by"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_file_type__epl2
msgid "EPL2"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_documentation_type__etd
msgid "Electronic Trade Documents"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_email_notifications
msgid "Email Notifications"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/delivery_fedex.py:0
msgid ""
"Error(s) from FedEx:\n"
"%s"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Extra Data"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_extra_data_rate_request
msgid "Extra data for rate"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_extra_data_return_request
msgid "Extra data for return"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_extra_data_ship_request
msgid "Extra data for ship"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__delivery_type__fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__stock_package_type__package_carrier_type__fedex_rest
msgid "FedEx"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_1_day_freight
msgid "FedEx 1Day® Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_2_day
msgid "FedEx 2Day®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_2_day_am
msgid "FedEx 2Day® AM"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_2_day_freight
msgid "FedEx 2Day® Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_3_day_freight
msgid "FedEx 3Day® Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_access_token
msgid "FedEx Access Token"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_account_number
msgid "FedEx Account Number"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_droppoff_type
msgid "FedEx Drop-Off Type"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_economy
msgid "FedEx Economy"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_economy_select
msgid "FedEx Economy (Only U.K.)"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_economy_freight
msgid "FedEx Economy Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__europe_first_international_priority
msgid "FedEx Europe First®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_express_saver
msgid "FedEx Express Saver®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_first
msgid "FedEx First"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__first_overnight
msgid "FedEx First Overnight®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_first_freight
msgid "FedEx First Overnight® Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__smart_post
msgid "FedEx Ground® Economy (Formerly known as FedEx SmartPost®)"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__ground_home_delivery
msgid "FedEx Home Delivery® "
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_international_connect_plus
msgid "FedEx International Connect Plus®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__international_economy_distribution
msgid "FedEx International Economy DirectDistribution"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__international_economy_freight
msgid "FedEx International Economy® Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__international_first
msgid "FedEx International First®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_ground
msgid "FedEx International Ground® and FedEx Domestic Ground®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__international_priority_distribution
msgid "FedEx International Priority DirectDistribution®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__international_distribution_freight
msgid "FedEx International Priority DirectDistribution® Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_international_priority
msgid "FedEx International Priority®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_international_priority_express
msgid "FedEx International Priority® Express"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__international_priority_freight
msgid "FedEx International Priority® Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_default_package_type_id
msgid "FedEx Package Type"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_priority
msgid "FedEx Priority"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_priority_express
msgid "FedEx Priority Express"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_priority_express_freight
msgid "FedEx Priority Express Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_priority_freight
msgid "FedEx Priority Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__priority_overnight
msgid "FedEx Priority Overnight®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__same_day
msgid "FedEx SameDay®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__same_day_city
msgid "FedEx SameDay® City"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_service_type
msgid "FedEx Service Type"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__standard_overnight
msgid "FedEx Standard Overnight®"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_international_deferred_freight
msgid "FedEx® International Deferred Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__international_economy
msgid "FedEx® International Economy"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_regional_economy
msgid "FedEx® Regional Economy"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__fedex_regional_economy_freight
msgid "FedEx® Regional Economy Freight"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_uom_uom__fedex_code
msgid "Fedex Code"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Fedex Configuration"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Fedex Developer Website"
msgstr ""

#. module: delivery_fedex_rest
#: model:delivery.carrier,name:delivery_fedex_rest.delivery_carrier_fedex_inter
#: model:product.template,name:delivery_fedex_rest.product_product_delivery_fedex_inter_product_template
msgid "Fedex International"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_duty_payment
msgid "Fedex Rest Duty Payment"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_weight_unit
msgid "Fedex Rest Weight Unit"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Fedex Tutorial"
msgstr ""

#. module: delivery_fedex_rest
#: model:delivery.carrier,name:delivery_fedex_rest.delivery_carrier_fedex_us
#: model:product.template,name:delivery_fedex_rest.product_product_delivery_fedex_us_product_template
msgid "Fedex US"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,help:delivery_fedex_rest.field_delivery_carrier__fedex_rest_documentation_type
msgid ""
"For international shipments (or some intra-country shipments), a commercial "
"invoice might be required for customs clearance. This commercial invoice can"
" be generated by FedEx based on shipment data and returned as PDF for "
"printing and attaching to the shipment or manual electronic submission to "
"FedEx. It can also be submitted directly as ETD information to FedEx upon "
"shipment validation."
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_documentation_type
msgid "Generate invoice"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Go to"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__intl_ground_distribution
msgid "International Ground® Distribution (IGD)"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/fedex_request.py:0
msgid "Invalid syntax for FedEx extra data."
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_weight_unit__kg
msgid "Kilograms"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_label_file_type
msgid "Label File Type"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Label Format"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_label_stock_type
msgid "Label Size"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_residential_address__never
msgid "Never"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_documentation_type__none
msgid "No"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Options"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__paper_4x6
msgid "PAPER_4X6"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__paper_4x6_75
msgid "PAPER_4X6.75"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__paper_4x8
msgid "PAPER_4X8"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__paper_4x9
msgid "PAPER_4X9"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__paper_7x4_75
msgid "PAPER_7X4.75"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__paper_8_5x11_bottom_half_label
msgid "PAPER_8.5X11_BOTTOM_HALF_LABEL"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__paper_8_5x11_top_half_label
msgid "PAPER_8.5X11_TOP_HALF_LABEL"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__paper_letter
msgid "PAPER_LETTER"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_file_type__pdf
msgid "PDF"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_file_type__png
msgid "PNG"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Package Weight Unit"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/delivery_fedex.py:0
msgid "Packages:"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_droppoff_type__use_scheduled_pickup
msgid "Part of regular scheduled pickup"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_weight_unit__lb
msgid "Pounds"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_documentation_type__invoice
msgid "Print PDF"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model,name:delivery_fedex_rest.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_duty_payment__recipient
msgid "Recipient"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/delivery_fedex.py:0
msgid "Required documents:"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_residential_address
msgid "Residential delivery"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/delivery_fedex.py:0
msgid "Return Label"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__stock_4x6
msgid "STOCK_4X6"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__stock_4x6_75
msgid "STOCK_4X6.75"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__stock_4x6_75_leading_doc_tab
msgid "STOCK_4X6.75_LEADING_DOC_TAB"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__stock_4x6_75_trailing_doc_tab
msgid "STOCK_4X6.75_TRAILING_DOC_TAB"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__stock_4x8
msgid "STOCK_4X8"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__stock_4x9
msgid "STOCK_4X9"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__stock_4x9_leading_doc_tab
msgid "STOCK_4X9_LEADING_DOC_TAB"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_stock_type__stock_4x9_trailing_doc_tab
msgid "STOCK_4X9_TRAILING_DOC_TAB"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "Screenshot"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_developer_password
msgid "Secret Key"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_duty_payment__sender
msgid "Sender"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/delivery_fedex.py:0
msgid "Shipment %s has been cancelled"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/delivery_fedex.py:0
msgid "Shipment created into Fedex"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model,name:delivery_fedex_rest.model_delivery_carrier
msgid "Shipping Methods"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/fedex_request.py:0
msgid "Something went wrong, please try again later!!"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model,name:delivery_fedex_rest.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,help:delivery_fedex_rest.field_delivery_carrier__fedex_rest_extra_data_rate_request
#: model:ir.model.fields,help:delivery_fedex_rest.field_delivery_carrier__fedex_rest_extra_data_return_request
#: model:ir.model.fields,help:delivery_fedex_rest.field_delivery_carrier__fedex_rest_extra_data_ship_request
msgid ""
"The extra data in FedEx is organized like the inside of a json file.\n"
"This functionality is advanced/technical and should only be used if you know what you are doing.\n"
"More info on https://www.developer.fedex.com"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/delivery_fedex.py:0
msgid "Tracking Numbers:"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_service_type__transborder_distribution
msgid "Transborder distribution"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,field_description:delivery_fedex_rest.field_delivery_carrier__fedex_rest_override_shipper_vat
msgid "Union tax id (EORI/IOSS)"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,help:delivery_fedex_rest.field_uom_uom__fedex_code
msgid "UoM Code sent to FedEx"
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/delivery_fedex.py:0
msgid ""
"Warning(s) from FedEx:\n"
"%s"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,help:delivery_fedex_rest.field_delivery_carrier__fedex_rest_email_notifications
msgid ""
"When enabled, the customer will receive email notifications from FedEx about"
" this shipment (when an email address is configured on the customer)"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields,help:delivery_fedex_rest.field_delivery_carrier__fedex_rest_override_shipper_vat
msgid ""
"Will be provided to Fedex as primary company tax identifier of type "
"BUSINESS_UNION to put on the generated invoice. Use this when you need to "
"use an IOSS or EORI number in addition to the national tax number. When not "
"provided the regular tax id on the company will be used with type "
"BUSINESS_NATIONAL."
msgstr ""

#. module: delivery_fedex_rest
#. odoo-python
#: code:addons/delivery_fedex_rest/models/fedex_request.py:0
msgid "You must setup a client ID and secret on the carrier first"
msgstr ""

#. module: delivery_fedex_rest
#: model:ir.model.fields.selection,name:delivery_fedex_rest.selection__delivery_carrier__fedex_rest_label_file_type__zplii
msgid "ZPLII"
msgstr ""

#. module: delivery_fedex_rest
#: model_terms:ir.ui.view,arch_db:delivery_fedex_rest.view_delivery_carrier_form_with_provider_fedex
msgid "to create a FedEx developer account"
msgstr ""
