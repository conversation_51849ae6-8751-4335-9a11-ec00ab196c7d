# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment_account_payment
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 18:46+0000\n"
"PO-Revision-Date: 2025-06-13 18:46+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/calendar_booking.py:0
msgid ""
"%(name)s\n"
"%(date_start)s at %(time_start)s to\n"
"%(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/calendar_booking.py:0
msgid ""
"%(name)s with %(staff_user)s\n"
"%(date_start)s at %(time_start)s to\n"
"%(date_end)s at %(time_end)s (%(timezone)s)"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "<i class=\"fa fa-check-circle text-success me-3\"/>Appointment Scheduled!"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "<i class=\"fa fa-pencil fa-fw me-1\"/>Edit my appointment"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid ""
"<i class=\"fa fa-pencil-square-o text-warning me-3\"/>Appointment waiting "
"for payment"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "<i class=\"fa fa-times-circle text-danger me-3\"/>Appointment Unavailable!"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_type_view_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/> Configure Payment "
"Providers"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_type_view_form
msgid ""
"<span invisible=\"resource_manage_capacity\" class=\"ms-1\">per booking</span>\n"
"                            <span invisible=\"not resource_manage_capacity\" class=\"ms-1\">per seat</span>"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "<span>Online</span>"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_progress_bar
msgid ""
"<span>Payment</span>\n"
"            <span class=\"d-inline-block mx-sm-3 fa fa-angle-right text-muted fs-5\"/>"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid ""
"<strong>Warning</strong> Make sure you are logged in as the right partner "
"before making this payment."
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__booking_token
msgid "Access Token"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.constraint,message:appointment_account_payment.constraint_appointment_type_check_product_and_payment_step
msgid "Activating the payment step requires a product"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "Amount"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view_form
msgid "Answers"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_appointment_answer_input
msgid "Appointment Answer Inputs"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__appointment_answer_input_ids
msgid "Appointment Answers"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__appointment_invite_id
msgid "Appointment Invite"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__account_move_id
msgid "Appointment Invoice"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_appointment_type
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__appointment_type_id
msgid "Appointment Type"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__asked_capacity
msgid "Asked Capacity"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "Attendees"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view_form
msgid "Booking"
msgstr ""

#. module: appointment_account_payment
#: model:product.template,name:appointment_account_payment.default_booking_product_product_template
msgid "Booking Fees"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__booking_line_ids
msgid "Booking Lines"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_type__product_id
msgid "Booking Product"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_answer_input__calendar_event_id
msgid "Calendar Event"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__capacity_reserved
msgid "Capacity Reserved"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__capacity_used
msgid "Capacity Used"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_form
msgid "Confirm Appointment"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__partner_id
msgid "Contact"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__create_uid
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__create_uid
msgid "Created by"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__create_date
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__create_date
msgid "Created on"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_type__product_currency_id
msgid "Currency"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__name
msgid "Customer Name"
msgstr ""

#. module: appointment_account_payment
#: model_terms:appointment.type,message_intro:appointment_account_payment.appointment_type_online_cooking_lesson
msgid ""
"Discover the secrets kept in high-end kitchens with one of our starred "
"chefs, from the comfort of your own home."
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__display_name
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__display_name
msgid "Display Name"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__duration
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "Duration"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "For"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__guest_ids
msgid "Guests"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__id
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__id
msgid "ID"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__not_available
msgid "Is Not Available"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__write_uid
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__write_uid
msgid "Last Updated by"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__write_date
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__write_date
msgid "Last Updated on"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Make customers pay a fee per person when booking your resources"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Make sure customers pay before they can take a slot in your calendar"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__calendar_event_id
msgid "Meeting"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_calendar_booking
#: model:ir.model.fields,field_description:appointment_account_payment.field_account_bank_statement_line__calendar_booking_ids
#: model:ir.model.fields,field_description:appointment_account_payment.field_account_move__calendar_booking_ids
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_answer_input__calendar_booking_id
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__calendar_booking_id
msgid "Meeting Booking"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_calendar_booking_line
msgid "Meeting Resource Booking"
msgstr ""

#. module: appointment_account_payment
#: model:appointment.type,name:appointment_account_payment.appointment_type_online_cooking_lesson
#: model:product.template,name:appointment_account_payment.product_appointment_type_online_cooking_lesson_product_template
msgid "Online Cooking Lesson"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "Oops! Paid appointment cannot be cancelled via our website.<br/>"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__staff_user_id
msgid "Operator"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Paid Consultation"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Paid Seats"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "Payment for \""
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_type_view_form
msgid "Pick a Product"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_form
msgid "Proceed to Payment"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_product_template
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__product_id
msgid "Product"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model,name:appointment_account_payment.model_product_product
msgid "Product Variant"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,help:appointment_account_payment.field_appointment_type__has_payment_step
msgid "Require visitors to pay to confirm their booking"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking_line__appointment_resource_id
msgid "Resource"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view_form
msgid "Resources"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/templates/appointment_type.py:0
msgid "Room %s"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_type__product_lst_price
msgid "Sales Price"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "Service"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__start
msgid "Start"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_calendar_booking__stop
msgid "Stop"
msgstr ""

#. module: appointment_account_payment
#: model_terms:appointment.type,message_confirmation:appointment_account_payment.appointment_type_online_cooking_lesson
msgid ""
"Thank you for your reservation. We will soon contact you to discuss menu "
"options."
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.constraint,message:appointment_account_payment.constraint_appointment_answer_input_check_event_or_booking
msgid "The answer inputs must be linked to a meeting or to a booking"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/calendar_booking.py:0
msgid ""
"The following booking was not confirmed due to insufficient availability or "
"configuration changes: %s"
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,help:appointment_account_payment.field_appointment_type__product_lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "There is nothing to pay."
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "This invoice is already paid for."
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_payment
msgid "This is a preview of the customer appointment payment form."
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "To make any changes, please contact"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "To make any changes, please contact us."
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid ""
"Unfortunately, it looks like this booking is not possible anymore. Please "
"contact us to find an alternative."
msgstr ""

#. module: appointment_account_payment
#: model:ir.model.fields,field_description:appointment_account_payment.field_appointment_type__has_payment_step
msgid "Up-front Payment"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "When"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "Where"
msgstr ""

#. module: appointment_account_payment
#. odoo-python
#: code:addons/appointment_account_payment/models/product_product.py:0
#: code:addons/appointment_account_payment/models/product_template.py:0
msgid "You cannot delete the default booking product"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "at"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_validated
msgid "or"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.calendar_booking_view
msgid "people"
msgstr ""

#. module: appointment_account_payment
#: model_terms:ir.ui.view,arch_db:appointment_account_payment.appointment_info
msgid "per seat"
msgstr ""
