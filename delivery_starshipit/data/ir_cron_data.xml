<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">

    <record id="ir_cron_starshipit_fetch_pending_prices" model="ir.cron">
        <field name="name">Starshipit: Fetch Pending Shipping Prices</field>
        <field name="model_id" ref="stock.model_stock_picking"/>
        <field name="state">code</field>
        <field name="code">model._cron_starshipit_fetch_and_update_prices()</field>
        <field name="user_id" ref="base.user_root"/>
        <field name="interval_number">1</field>
        <field name="interval_type">hours</field>
        <field name="nextcall" eval="(DateTime.now() + timedelta(minutes=5)).strftime('%Y-%m-%d %H:%M:%S')" />
        <field name="active" eval="True"/>
    </record>

</odoo>
