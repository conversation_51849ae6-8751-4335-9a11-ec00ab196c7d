# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_ups_rest
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:24+0000\n"
"PO-Revision-Date: 2024-09-25 09:24+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,field_description:delivery_ups_rest.field_res_partner__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups_rest.field_res_users__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups_rest.field_sale_order__ups_bill_my_account
msgid "Bill My Account"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_cod_funds_code
msgid "COD Funding Option"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_cod_funds_code__8
msgid "Cashier's Check or MoneyOrder"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_package_dimension_unit__cm
msgid "Centimeters"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_cod_funds_code__0
msgid "Check, Cashier's Check or MoneyOrder"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_cod
msgid "Collect on Delivery"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model,name:delivery_ups_rest.model_res_partner
msgid "Contact"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid "Could not decode response"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Create an account on"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Create an app"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Duties paid by"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_label_file_type__epl
msgid "EPL"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/delivery_ups.py:0
msgid ""
"Error:\n"
"%s"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Fill your address and contact information"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "From there, get your <b>account number</b>"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "How to setup UPS?"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,help:delivery_ups_rest.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,help:delivery_ups_rest.field_res_partner__bill_my_account
#: model:ir.model.fields,help:delivery_ups_rest.field_res_users__bill_my_account
#: model:ir.model.fields,help:delivery_ups_rest.field_sale_order__ups_bill_my_account
msgid ""
"If checked, ecommerce users will be prompted their UPS account number\n"
"and delivery fees will be charged on it."
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_package_dimension_unit__in
msgid "Inches"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid ""
"Invalid Authentication Information: Please check your credentials and "
"configuration within UPS's system."
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_package_weight_unit__kgs
msgid "Kilograms"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Label Format"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Log in with your account on"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Name your app,"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid ""
"Now go to odoo and create a shipping method for UPS, using Account Number "
"(step2) and Client ID + Client Secret (step 8). Also ensure you use correct "
"dimensions and weight unit for your country"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid ""
"Now open your app, you'll have your <b>Client ID</b> and <b>Client "
"Secret</b>"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Options"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_label_file_type__gif
msgid "PDF"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_package_dimension_unit
msgid "Package Size Unit"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Package Weight Unit"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid "Packages %s do not have a positive shipping weight."
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_package_weight_unit__lbs
msgid "Pounds"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_duty_payment__recipient
msgid "Recipient"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid "Recipient Phone must be at least 10 alphanumeric characters."
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/delivery_ups.py:0
msgid ""
"Return label generated<br/><b>Tracking Numbers:</b> "
"%(tracking_numbers)s<br/><b>Packages:</b> %(packages)s"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_label_file_type__spl
msgid "SPL"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model,name:delivery_ups_rest.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Saturday Delivery"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "Save and Accept terms and your app is created!"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_duty_payment__sender
msgid "Sender"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/delivery_ups.py:0
msgid "Shipment #%s has been cancelled"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/delivery_ups.py:0
msgid ""
"Shipment created into UPS<br/><b>Tracking Numbers:</b> "
"%(tracking_numbers)s<br/><b>Packages:</b> %(packages)s"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid "Shipper Phone must be at least 10 alphanumeric characters."
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model,name:delivery_ups_rest.model_delivery_carrier
msgid "Shipping Methods"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid "Something went wrong, please try again later!!"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model,name:delivery_ups_rest.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid ""
"The delivery cannot be done because the weight of your product %s is "
"missing."
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid ""
"The estimated price cannot be computed because the weight of your product %s"
" is missing."
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid "The name of the customer should be no more than 35 characters."
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,help:delivery_ups_rest.field_delivery_carrier__ups_cod
msgid ""
"This value added service enables UPS to collect the payment of the shipment "
"from your customer."
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,help:delivery_ups_rest.field_delivery_carrier__ups_saturday_delivery
msgid ""
"This value added service will allow you to ship the package on saturday "
"also."
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__delivery_type__ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__stock_package_type__package_carrier_type__ups_rest
msgid "UPS"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__02
msgid "UPS 2nd Day"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__59
msgid "UPS 2nd Day AM"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__12
msgid "UPS 3-day Select"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_access_token
msgid "UPS Access Token"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_shipper_number
#: model:ir.model.fields,field_description:delivery_ups_rest.field_res_partner__property_ups_carrier_account
#: model:ir.model.fields,field_description:delivery_ups_rest.field_res_users__property_ups_carrier_account
msgid "UPS Account Number"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_client_id
msgid "UPS Client ID"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_client_secret
msgid "UPS Client Secret"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "UPS Configuration"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__03
msgid "UPS Ground"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_label_file_type
msgid "UPS Label File Type"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__01
msgid "UPS Next Day"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__14
msgid "UPS Next Day AM"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__13
msgid "UPS Next Day Air Saver"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_default_packaging_id
msgid "UPS Package Type"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_saturday_delivery
msgid "UPS Saturday Delivery"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__65
msgid "UPS Saver"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_default_service_type
msgid "UPS Service Type"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__11
msgid "UPS Standard"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__08
msgid "UPS Worldwide Expedited"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__07
msgid "UPS Worldwide Express"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__96
msgid "UPS Worldwide Express Freight"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_default_service_type__54
msgid "UPS Worldwide Express Plus"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_sale_order__partner_ups_carrier_account
msgid "UPS account number"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid ""
"UPS address lines can only contain a maximum of 35 characters. You can split"
" the contacts addresses on multiple lines to try to avoid this limitation."
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_duty_payment
msgid "Ups Duty Payment"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields,field_description:delivery_ups_rest.field_delivery_carrier__ups_package_weight_unit
msgid "Ups Package Weight Unit"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid "Warehouse Phone must be at least 10 alphanumeric characters."
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/sale_order.py:0
msgid "You must enter an UPS account number."
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/ups_request.py:0
msgid "You must setup a client ID and secret on the carrier first"
msgstr ""

#. module: delivery_ups_rest
#: model:ir.model.fields.selection,name:delivery_ups_rest.selection__delivery_carrier__ups_label_file_type__zpl
msgid "ZPL"
msgstr ""

#. module: delivery_ups_rest
#. odoo-python
#: code:addons/delivery_ups_rest/models/sale_order.py:0
msgid "[UPS] UPS Billing will remain to the customer"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid ""
"add products <b>Authorization, Address validation, Locator, Paperless "
"Documents, Shipping and Rating</b>"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "and go to Apps"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "developer.ups.com"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "the callback is your odoo address https://\"mycompany\".odoo.com"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "ups step 1"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "ups step 2"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "ups step 3"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "ups step 4"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "ups step 5"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "ups step 6"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "ups.com"
msgstr ""

#. module: delivery_ups_rest
#: model_terms:ir.ui.view,arch_db:delivery_ups_rest.view_delivery_carrier_form
msgid "you're ready to go!"
msgstr ""
