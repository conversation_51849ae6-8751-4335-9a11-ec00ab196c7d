# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment_hr_recruitment
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-12-19 09:52+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: appointment_hr_recruitment
#: model:mail.template,body_html:appointment_hr_recruitment.email_template_data_applicant_schedule_interview
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <h2>Hello,</h2>\n"
"            <div style=\"font-size: 13px;\">\n"
"                Thanks for your application and interest in <t t-out=\"object.company_id.name or ''\">YourCompany</t>.<br/>\n"
"                <br/>\n"
"                I would like to organize a phone interview with you, it should last about 30 min.\n"
"                We'll go through your resume and motivations to join the <t t-out=\"object.company_id.name or ''\">YourCompany</t> team.\n"
"                You can book a slot in our agenda by cliking on the button below.<br/>\n"
"            </div>\n"
"            <br/>\n"
"            <a t-att-href=\"env['ir.config_parameter'].sudo().get_param('web.base.url') + '/book/interview'\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Schedule my interview</a><br/>\n"
"            <br/>\n"
"            Best regards,\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: appointment_hr_recruitment
#: model:ir.model.fields,field_description:appointment_hr_recruitment.field_appointment_invite__applicant_id
#: model:ir.model.fields,field_description:appointment_hr_recruitment.field_calendar_event__applicant_id
msgid "Applicant"
msgstr ""

#. module: appointment_hr_recruitment
#: model:ir.model,name:appointment_hr_recruitment.model_appointment_invite
msgid "Appointment Invite"
msgstr ""

#. module: appointment_hr_recruitment
#: model:ir.model,name:appointment_hr_recruitment.model_appointment_type
msgid "Appointment Type"
msgstr ""

#. module: appointment_hr_recruitment
#: model:ir.model,name:appointment_hr_recruitment.model_calendar_event
msgid "Calendar Event"
msgstr ""

#. module: appointment_hr_recruitment
#: model:mail.template,subject:appointment_hr_recruitment.email_template_data_applicant_schedule_interview
msgid ""
"Can we plan an interview together for your {{ (object.job_id.name + ' ') if "
"object.job_id else '' }}application?"
msgstr ""

#. module: appointment_hr_recruitment
#: model:appointment.type,name:appointment_hr_recruitment.appointment_type_interviews
msgid "Interviews availabilities"
msgstr ""

#. module: appointment_hr_recruitment
#: model:ir.model.fields,help:appointment_hr_recruitment.field_appointment_invite__applicant_id
#: model:ir.model.fields,help:appointment_hr_recruitment.field_calendar_event__applicant_id
msgid ""
"Link an applicant to the appointment invite created.\n"
"Used when creating an invitation from the Meeting action in the applicant form view."
msgstr ""

#. module: appointment_hr_recruitment
#: model:mail.template,name:appointment_hr_recruitment.email_template_data_applicant_schedule_interview
msgid "Recruitment: Schedule interview"
msgstr ""

#. module: appointment_hr_recruitment
#: model:mail.template,description:appointment_hr_recruitment.email_template_data_applicant_schedule_interview
msgid ""
"Send this email when you want to schedule an interview with a candidate"
msgstr ""
