# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_extract
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2025-01-27 13:55+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model,name:account_bank_statement_extract.model_account_bank_statement
msgid "Bank Statement"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_res_config_settings__extract_bank_statement_digitalization_mode
msgid "Bank Statements"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model,name:account_bank_statement_extract.model_res_company
msgid "Companies"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model,name:account_bank_statement_extract.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_res_company__extract_bank_statement_digitalization_mode
msgid "Digitization mode on bank statements"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields.selection,name:account_bank_statement_extract.selection__res_company__extract_bank_statement_digitalization_mode__auto_send
msgid "Digitize automatically"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields.selection,name:account_bank_statement_extract.selection__res_company__extract_bank_statement_digitalization_mode__no_send
msgid "Do not digitize"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_error_message
msgid "Error message"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_state_processed
msgid "Extract State Processed"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_state
msgid "Extract state"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_status
msgid "Extract status"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_bank_statement_extract
#. odoo-python
#: code:addons/account_bank_statement_extract/models/account_journal.py:0
msgid "Generated Bank Statements"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__has_message
msgid "Has Message"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__is_in_extractable_state
msgid "Is In Extractable State"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model,name:account_bank_statement_extract.model_account_journal
msgid "Journal"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_ids
msgid "Messages"
msgstr ""

#. module: account_bank_statement_extract
#. odoo-python
#: code:addons/account_bank_statement_extract/models/account_journal.py:0
msgid "Mixing PDF/Image files with other file types is not allowed."
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__rating_ids
msgid "Ratings"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: account_bank_statement_extract
#. odoo-python
#: code:addons/account_bank_statement_extract/models/account_bank_statement.py:0
msgid ""
"Statement and transactions have been updated using Artificial Intelligence."
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,field_description:account_bank_statement_extract.field_account_bank_statement__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_bank_statement_extract
#: model:ir.model.fields,help:account_bank_statement_extract.field_account_bank_statement__website_message_ids
msgid "Website communication history"
msgstr ""
