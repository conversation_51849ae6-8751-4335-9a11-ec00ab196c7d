# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_disallowed_expenses_fleet
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:26+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_disallowed_expenses_fleet
#. odoo-python
#: code:addons/account_disallowed_expenses_fleet/report/account_disallowed_expenses_report.py:0
msgid "Accounts missing a disallowed expense category"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__company_id
msgid "Company"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__create_uid
msgid "Created by"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__create_date
msgid "Created on"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_deferred_report_handler
msgid "Deferred Expense Report Custom Handler"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_disallowed_expenses_category
msgid "Disallowed Expenses Category"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_disallowed_expenses_fleet_report_handler
msgid "Disallowed Expenses Fleet Custom Handler"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_vehicle__rate_ids
#: model_terms:ir.ui.view,arch_db:account_disallowed_expenses_fleet.fleet_vehicle_view_form
msgid "Disallowed Expenses Rate"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__display_name
msgid "Display Name"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__id
msgid "ID"
msgstr ""

#. module: account_disallowed_expenses_fleet
#. odoo-javascript
#: code:addons/account_disallowed_expenses_fleet/static/src/components/disallowed_expenses_report/warnings.xml:0
msgid ""
"It looks like Journal Items concerning vehicles are missing. Please check if"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_bank_rec_widget_line
msgid "Line of the bank reconciliation widget"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_account_disallowed_expenses_category__car_category
msgid "Make Vehicle Required"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__date_from
msgid "Start Date"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model.fields,help:account_disallowed_expenses_fleet.field_account_disallowed_expenses_category__car_category
msgid "The vehicle becomes mandatory while booking any account move."
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_fleet_vehicle
#: model:ir.model.fields,field_description:account_disallowed_expenses_fleet.field_fleet_disallowed_expenses_rate__vehicle_id
msgid "Vehicle"
msgstr ""

#. module: account_disallowed_expenses_fleet
#: model:ir.model,name:account_disallowed_expenses_fleet.model_fleet_disallowed_expenses_rate
msgid "Vehicle Disallowed Expenses Rate"
msgstr ""

#. module: account_disallowed_expenses_fleet
#. odoo-javascript
#: code:addons/account_disallowed_expenses_fleet/static/src/components/disallowed_expenses_report/filter_extra_options.xml:0
msgid "Vehicle Split"
msgstr ""

#. module: account_disallowed_expenses_fleet
#. odoo-javascript
#: code:addons/account_disallowed_expenses_fleet/static/src/components/disallowed_expenses_report/warnings.xml:0
msgid "need a Disallowed Expense Category."
msgstr ""

#. module: account_disallowed_expenses_fleet
#. odoo-javascript
#: code:addons/account_disallowed_expenses_fleet/static/src/components/disallowed_expenses_report/warnings.xml:0
msgid "these accounts"
msgstr ""
