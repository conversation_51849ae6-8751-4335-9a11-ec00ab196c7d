# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_invoice_extract
#
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2025-08-29 15:40+0000\n"
"Last-Translator: Oakarmin Iron <<EMAIL>>\n"
"Language-Team: Burmese <https://translate.odoo.com/projects/odoo-18/"
"account_invoice_extract/my/>\n"
"Language: my\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_can_show_banners
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_banners
msgid "Can show the ocr banners"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_company
msgid "Companies"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_invoice_extract
#. odoo-python
#: code:addons/account_invoice_extract/models/account_invoice.py:0
msgid "Couldn't reload AI data."
msgstr ""

#. module: account_invoice_extract
#. odoo-javascript
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_form_renderer.js:0
msgid "Create"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_uid
msgid "Created by"
msgstr "ဖန်တီးသူ"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_date
msgid "Created on"
msgstr "ဖန်တီးချိန်"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_out_invoice_digitalization_mode
msgid "Customer Invoices"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_out_invoice_digitalization_mode
msgid "Digitization mode on customer invoices"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_in_invoice_digitalization_mode
msgid "Digitization mode on vendor bills"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__auto_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__auto_send
msgid "Digitize automatically"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Digitize document"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__manual_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__manual_send
msgid "Digitize on demand only"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__display_name
msgid "Display Name"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__no_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__no_send
msgid "Do not digitize"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.res_config_settings_view_form
msgid "Enable to get only one invoice line per tax"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_error_message
msgid "Error message"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_attachment_id
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_attachment_id
msgid "Extract Attachment"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_detected_layout
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_detected_layout
msgid "Extract Detected Layout Id"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_partner_name
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_partner_name
msgid "Extract Detected Partner Name"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_prefill_data
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_prefill_data
msgid "Extract Prefill Data"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_state_processed
msgid "Extract State Processed"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_word_ids
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_word_ids
msgid "Extract Word"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_state
msgid "Extract state"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_status
msgid "Extract status"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_invoice_extract_words
msgid "Extracted words from invoice scan"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Extraction Information"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__field
msgid "Field"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__has_message
msgid "Has Message"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__id
msgid "ID"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_error
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__invoice_id
msgid "Invoice"
msgstr ""

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Invoice OCR: Update All Status"
msgstr ""

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Invoice OCR: Validate Invoices"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__is_in_extractable_state
msgid "Is In Extractable State"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_ids
msgid "Messages"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__ocr_selected
msgid "Ocr Selected"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__rating_ids
msgid "Ratings"
msgstr ""

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Reload AI Data"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.model_account_send_for_digitalization
msgid "Send Bills for digitization"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_single_line_per_tax
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_single_line_per_tax
msgid "Single Invoice Line Per Tax"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__user_selected
msgid "User Selected"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_in_invoice_digitalization_mode
msgid "Vendor Bills"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_angle
msgid "Word Box Angle"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_height
msgid "Word Box Height"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midX
msgid "Word Box Midx"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midY
msgid "Word Box Midy"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_width
msgid "Word Box Width"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_page
msgid "Word Page"
msgstr ""

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_text
msgid "Word Text"
msgstr ""

#. module: account_invoice_extract
#. odoo-python
#: code:addons/account_invoice_extract/models/account_invoice.py:0
msgid "You cannot send a expense that is not in draft state!"
msgstr ""
