# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_budget
#
# <AUTHOR> <EMAIL>, 2025.
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:13+0000\n"
"PO-Revision-Date: 2025-08-29 15:40+0000\n"
"Last-Translator: Oakarmin Iron <<EMAIL>>\n"
"Language-Team: Burmese <https://translate.odoo.com/projects/odoo-18/"
"account_budget/my/>\n"
"Language: my\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.12.2\n"

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_kanban
msgid "<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Period\" title=\"Period\"/>"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"to\" title=\"to\"/>"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "Account"
msgstr "အကောင့်"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__achieved_amount
#: model:ir.model.fields,field_description:account_budget.field_budget_report__achieved
#: model:ir.model.fields.selection,name:account_budget.selection__budget_report__line_type__achieved
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_pivot
msgid "Achieved"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__achieved_percentage
msgid "Achieved (%)"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_ids
msgid "Activities"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_state
msgid "Activity State"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_line__committed_amount
msgid "Already Billed amount + Confirmed purchase orders."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_line__achieved_amount
msgid "Amount Billed/Invoiced."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_line__theoritical_amount
msgid ""
"Amount supposed to be Billed/Invoiced, formula = (Budget Amount / Budget "
"Days) x Budget Days Completed"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_account_analytic_account
#: model:ir.model.fields,field_description:account_budget.field_budget_line__auto_account_id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__auto_account_id
msgid "Analytic Account"
msgstr ""

#. module: account_budget
#: model:ir.ui.menu,name:account_budget.menu_act_budget_analytic_view
msgid "Analytic Budget"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_purchase_order_line__analytic_json
msgid "Analytic JSON"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__analytical_plan_ids
msgid "Analytic Plans"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__budget_type__both
msgid "Both"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_budget_analytic
#: model:ir.model.fields,field_description:account_budget.field_budget_report__budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_report__line_type__budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.purchase_order_form_account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_tree
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_pivot
msgid "Budget"
msgstr ""

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/wizards/budget_split_wizard.py:0
msgid "Budget %s"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__budget_analytic_id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__budget_analytic_id
msgid "Budget Analytic"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_budget_line
#: model:ir.model.fields,field_description:account_budget.field_account_analytic_account__budget_line_ids
#: model:ir.model.fields,field_description:account_budget.field_budget_report__budget_line_id
msgid "Budget Line"
msgstr ""

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__budget_line_ids
#: model:ir.model.fields,field_description:account_budget.field_purchase_order_line__budget_line_ids
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_tree
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_form
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_graph
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_pivot
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_tree
msgid "Budget Lines"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__name
#: model:ir.model.fields,field_description:account_budget.field_budget_line__name
msgid "Budget Name"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.budget_report_action
#: model:ir.model,name:account_budget.model_budget_report
#: model:ir.ui.menu,name:account_budget.budget_report_menu
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_graph
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_pivot
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_tree
msgid "Budget Report"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_budget_split_wizard
msgid "Budget Split Wizard"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__budget_analytic_state
msgid "Budget State"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__budget_type
msgid "Budget Type"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__budget_amount
msgid "Budgeted"
msgstr ""

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/wizards/budget_split_wizard.py:0
#: model:ir.actions.act_window,name:account_budget.act_budget_analytic_view
#: model_terms:ir.ui.view,arch_db:account_budget.view_account_analytic_account_form_inherit_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
msgid "Budgets"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.act_budget_lines_view
msgid "Budgets Analysis"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
msgid "Cancel"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Cancel Budget"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__canceled
msgid "Canceled"
msgstr ""

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_budget_analytic_view
msgid "Click to create a new budget."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__committed_amount
#: model:ir.model.fields,field_description:account_budget.field_budget_report__committed
#: model:ir.model.fields.selection,name:account_budget.selection__budget_report__line_type__committed
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Committed"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__committed_percentage
msgid "Committed (%)"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__company_id
#: model:ir.model.fields,field_description:account_budget.field_budget_line__company_id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__company_id
msgid "Company"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__create_uid
#: model:ir.model.fields,field_description:account_budget.field_budget_line__create_uid
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__create_uid
msgid "Created by"
msgstr "ဖန်တီးသူ"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__create_date
#: model:ir.model.fields,field_description:account_budget.field_budget_line__create_date
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__create_date
msgid "Created on"
msgstr "ဖန်တီးချိန်"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__currency_id
msgid "Currency"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__date
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
msgid "Date"
msgstr ""

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
msgid "Deletion is only allowed in the Draft and Canceled stages."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__description
msgid "Description"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Details..."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__display_name
#: model:ir.model.fields,field_description:account_budget.field_budget_line__display_name
#: model:ir.model.fields,field_description:account_budget.field_budget_report__display_name
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__display_name
msgid "Display Name"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__res_id
msgid "Document"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__done
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Done"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__draft
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Draft"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Draft Budgets"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__date_to
#: model:ir.model.fields,field_description:account_budget.field_budget_line__date_to
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__date_to
msgid "End Date"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__budget_type__expense
msgid "Expense"
msgstr "အသုံးစရိတ်"

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
msgid "From"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Future Activities"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_tree
msgid "Generate"
msgstr ""

#. module: account_budget
#: model:ir.actions.act_window,name:account_budget.budget_split_wizard_action
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
msgid "Generate Budget"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
msgid "Group By"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__has_message
msgid "Has Message"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__id
#: model:ir.model.fields,field_description:account_budget.field_budget_line__id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__id
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__id
msgid "ID"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_has_error
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "In a Budget"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__is_above_budget
#: model:ir.model.fields,field_description:account_budget.field_purchase_order__is_above_budget
#: model:ir.model.fields,field_description:account_budget.field_purchase_order_line__is_above_budget
msgid "Is Above Budget"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_purchase_order__is_analytic
msgid "Is Analytic"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__write_uid
#: model:ir.model.fields,field_description:account_budget.field_budget_line__write_uid
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__write_date
#: model:ir.model.fields,field_description:account_budget.field_budget_line__write_date
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Late Activities"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_ids
msgid "Messages"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__res_model
msgid "Model"
msgstr "မော်ဒယ်"

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_split_wizard__period__month
msgid "Month"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
msgid "New revision"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_search
msgid "Not Cancelled"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__confirmed
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Open"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "Open Budget"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__period
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Period"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_line_graph
msgid "Practical amount"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__account_id
#: model:ir.model.fields,field_description:account_budget.field_budget_report__account_id
msgid "Project Account"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_purchase_order
msgid "Purchase Order"
msgstr ""

#. module: account_budget
#: model:ir.model,name:account_budget.model_purchase_order_line
msgid "Purchase Order Line"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_split_wizard__period__quarter
msgid "Quarter"
msgstr ""

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
msgid "REV %s"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__rating_ids
msgid "Ratings"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Reset to Draft"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__user_id
msgid "Responsible"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__budget_type__revenue
msgid "Revenue"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Revise"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_analytic__state__revised
msgid "Revised"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__parent_id
msgid "Revision Of"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__children_ids
msgid "Revisions"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__sequence
msgid "Sequence"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_split_wizard_form_view
msgid "Split"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__date_from
#: model:ir.model.fields,field_description:account_budget.field_budget_line__date_from
#: model:ir.model.fields,field_description:account_budget.field_budget_split_wizard__date_from
msgid "Start Date"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__state
msgid "Status"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_line.py:0
msgid "The 'End Date' must be greater than or equal to 'Start Date'."
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__theoritical_amount
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "Theoretical"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_line__theoritical_percentage
msgid "Theoretical (%)"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_search
msgid "Today Activities"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_tree
msgid "Total Achieved"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_tree
msgid "Total Budget"
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_tree
msgid "Total Committed"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__line_type
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "Type"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: account_budget
#: model_terms:ir.actions.act_window,help:account_budget.act_budget_analytic_view
msgid "Use budgets to compare actual with expected revenues and costs"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_report__user_id
#: model_terms:ir.ui.view,arch_db:account_budget.budget_report_view_search
msgid "User"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,field_description:account_budget.field_budget_analytic__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_budget
#: model:ir.model.fields,help:account_budget.field_budget_analytic__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account_budget
#: model:ir.model.fields.selection,name:account_budget.selection__budget_split_wizard__period__year
msgid "Year"
msgstr "နှစ်"

#. module: account_budget
#. odoo-python
#: code:addons/account_budget/models/budget_analytic.py:0
msgid "You cannot create recursive revision of budget."
msgstr ""

#. module: account_budget
#: model_terms:ir.ui.view,arch_db:account_budget.view_budget_analytic_form
msgid "e.g. Budget 2023"
msgstr ""
