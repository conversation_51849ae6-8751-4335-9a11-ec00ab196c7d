# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* appointment_google_reserve
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-15 18:45+0000\n"
"PO-Revision-Date: 2025-08-15 18:45+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: appointment_google_reserve
#. odoo-python
#: code:addons/appointment_google_reserve/models/appointment_type.py:0
msgid ""
"%(appointment_type_name)s: A %(appointment_category)s appointment type "
"cannot be used with the Google Bookings integration."
msgstr ""

#. module: appointment_google_reserve
#. odoo-python
#: code:addons/appointment_google_reserve/models/appointment_type.py:0
msgid ""
"%(appointment_type_name)s: Please configure your Google Reserve Merchant for"
" Google Reserve."
msgstr ""

#. module: appointment_google_reserve
#. odoo-python
#: code:addons/appointment_google_reserve/models/appointment_type.py:0
msgid ""
"%(appointment_type_name)s: The Google Bookings integration only works in the"
" auto-assign mode."
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid "<i class=\"ms-2 fa fa-info-circle\" title=\"Required for Google Reserve\"/>"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid ""
"<span class=\"fa fa-check fa-fw me-1\"/>Your Google Reserve Synchronization "
"is active."
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid ""
"<span class=\"fa fa-check fa-hourglass me-1\"/>Your Appointment Type is being synchronized with Google Reserve.<br/>\n"
"                                This can take up to 24 hours to be active."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_needaction
msgid "Action Needed"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__location_id
msgid "Address"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model,name:appointment_google_reserve.model_appointment_resource
msgid "Appointment Resource"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model,name:appointment_google_reserve.model_appointment_type
msgid "Appointment Type"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.actions.server,name:appointment_google_reserve.ir_cron_iap_google_reserve_availabilities_ir_actions_server
msgid "Appointment: Upload Availabilities to IAP for Google Reserve"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__appointment_type_ids
msgid "Appointments"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_appointment_type__assign_method
msgid "Assignment Method"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__business_category
msgid "Business Category"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model,name:appointment_google_reserve.model_calendar_event
msgid "Calendar Event"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_appointment_type__google_reserve_merchant_id
msgid "Configure your company data for Google Reserve"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__create_uid
msgid "Created by"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__create_date
msgid "Created on"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid "Disable Google Reserve"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid "Discard"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__display_name
msgid "Display Name"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_appointment_type__google_reserve_enable
msgid "Enable Google Booking"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_follower_ids
msgid "Followers"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_calendar_event__is_google_reserve
msgid "From Google Reserve"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_appointment_type__google_reserve_pending_sync
msgid "Google Booking Pending Synchronization"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid "Google Bookings"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.ui.menu,name:appointment_google_reserve.menu_appointment_google_reserve_merchants
msgid "Google Reserve"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_appointment_type__google_reserve_access_token
msgid "Google Reserve Access Token"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model,name:appointment_google_reserve.model_google_reserve_merchant
#: model:ir.model.fields,field_description:appointment_google_reserve.field_appointment_type__google_reserve_merchant_id
msgid "Google Reserve Merchant"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.actions.act_window,name:appointment_google_reserve.google_reserve_merchant_action
msgid "Google Reserve Merchants"
msgstr ""

#. module: appointment_google_reserve
#. odoo-python
#: code:addons/appointment_google_reserve/models/google_reserve_merchant.py:0
msgid ""
"Google Reserve requires a complete address.Including a Country, a City, a "
"Postal Code and a Street"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__has_message
msgid "Has Message"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid ""
"Hold a second — are you sure you want to stop showing on Google Reserve? "
"Changes can take up to 24 hours to take effect."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_appointment_type__assign_method
msgid ""
"How users and resources will be assigned to meetings customers book on your "
"website."
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid "I'm ready, synchronize!"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__id
msgid "ID"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__message_has_error
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__write_uid
msgid "Last Updated by"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__write_date
msgid "Last Updated on"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__name
msgid "Merchant Name"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_ids
msgid "Messages"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid "Not yet"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: appointment_google_reserve
#. odoo-python
#: code:addons/appointment_google_reserve/models/appointment_type.py:0
msgid ""
"Only Appointment Managers can enable or disable the Google Reserve "
"Integration"
msgstr ""

#. module: appointment_google_reserve
#. odoo-python
#: code:addons/appointment_google_reserve/models/appointment_type.py:0
msgid "Only Appointment Managers can enable the Google Reserve Integration"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__phone
msgid "Phone"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__phone_mobile_search
msgid "Phone/Mobile"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__rating_ids
msgid "Ratings"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid ""
"Ready to synchronize your Appointment Type with Google Reserve? Make sure "
"your setup is complete — changes may take up to 24 hours to appear once "
"live."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model,name:appointment_google_reserve.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr ""

#. module: appointment_google_reserve
#: model:appointment.type,name:appointment_google_reserve.appointment_type_restaurant
msgid "Rest'Odoo"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__phone_sanitized
msgid "Sanitized Number"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid "Stop syncing"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.appointment_type_view_form
msgid "Synchronize with Google Reserve"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__phone
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__website_url
msgid "Used by Google to properly match merchants and partners, recommended."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__business_category
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__location_id
msgid "Used by Google to try to match your physical address to your business."
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,field_description:appointment_google_reserve.field_google_reserve_merchant__website_url
msgid "Website URL"
msgstr ""

#. module: appointment_google_reserve
#: model:ir.model.fields,help:appointment_google_reserve.field_google_reserve_merchant__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: appointment_google_reserve
#: model_terms:ir.ui.view,arch_db:appointment_google_reserve.google_reserve_merchant_view_form
msgid "e.g Restaurant"
msgstr ""
